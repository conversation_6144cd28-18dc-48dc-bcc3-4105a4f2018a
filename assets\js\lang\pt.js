// Portuguese language file
export default {
  dir: "ltr",
  settings: { title: "Configurações", save: "Salvar", close: "<PERSON><PERSON><PERSON>", language: "Idioma" },
  info: {
    title: "Informações",
    pwaInstall: {
      title: "Instalar como App",
      description: "Instale o scTimer como um Progressive Web App para a melhor experiência.",
      install: "Instalar App",
    },
    shortcuts: { title: "Atalhos do Teclado", timer: "Controles do Timer", spacebar: "Iniciar/parar timer" },
    features: { title: "Recursos Principais", timer: "Timer Profissional", puzzles: "Todos os Eventos WCA" },
  },
  timerOptions: {
    title: "Opções do Timer",
    useInspection: "Usar Inspeção WCA (15s)",
    timerMode: "Modo do Timer:",
    timerModeTimer: "Timer",
  },
  displayOptions: { title: "Opções de Exibição", darkMode: "Modo Escuro" },
  app: { title: "scTimer", description: "Um timer de speedcubing com inspeção WCA e estatísticas" },
  timer: { ready: "Pronto", running: "Executando", idle: "Inativo", inspection: "Inspeção" },
  stats: { title: "Estatísticas", best: "Melhor", worst: "Pior", mean: "Média", solves: "Resoluções" },
  statsDetails: { title: "Detalhes das Estatísticas", overview: "Visão Geral", averages: "Médias" },
  solveDetails: { title: "Detalhes da Resolução", time: "Tempo", date: "Data", scramble: "Embaralhamento" },
  times: { title: "Tempos de Resolução", clear: "Limpar Tempos", close: "Fechar" },
  buttons: { viewTimes: "Ver Tempos", ok: "OK", cancel: "Cancelar" },
  events: {
    333: "3×3×3", 222: "2×2×2", 444: "4×4×4", 555: "5×5×5", 666: "6×6×6", 777: "7×7×7",
    "333bf": "3×3×3 Vendado", "333fm": "3×3×3 Menos Movimentos", "333oh": "3×3×3 Uma Mão",
    clock: "Clock", minx: "Megaminx", pyram: "Pyraminx", skewb: "Skewb", sq1: "Square-1",
  },
  mbld: { cubeCount: "Cubos", points: "pontos", save: "Salvar Resultado" },
  modals: { error: "Erro", warning: "Aviso", info: "Informação", confirm: "Confirmar" },
  stackmat: { error: "Erro Stackmat", connected: "Conectado", disconnected: "Desconectado" },
  sessions: { newSessionTitle: "Nova Sessão", create: "Criar", save: "Salvar" },
  scramble: { loading: "Carregando embaralhamento..." },
  fmc: { title: "Desafio Menos Movimentos", moves: "movimentos", submit: "Enviar" },
  tutorial: {
    welcomeTitle: "Bem-vindo ao scTimer!",
    selectLanguage: "Selecionar Idioma:",
    skipTutorial: "Pular Tutorial",
    startTour: "Iniciar Tour",
    previous: "Anterior", next: "Próximo", finish: "Finalizar", close: "Fechar",
  },
};
