<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Best Mo3 Feature Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .feature-demo {
            background: #e8f5e8;
            border: 2px solid #4caf50;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .code-block {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
        .times-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .time-item {
            background: #f0f0f0;
            padding: 8px;
            text-align: center;
            border-radius: 4px;
            font-family: monospace;
        }
        .best-mo3 {
            background: #fff3cd;
            border: 2px solid #ffc107;
        }
        .calculation {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Best Mean of 3 (mo3) Feature Test</h1>
    
    <div class="test-section">
        <h2>Feature Overview</h2>
        <p>The <strong>Best mo3</strong> feature has been added to the overview section of the More Stats modal. This calculates the best mean of 3 consecutive solves from all your times.</p>
        
        <div class="feature-demo">
            <h3>✅ What's New</h3>
            <ul>
                <li>Added "Best mo3" stat card to the overview section</li>
                <li>Calculates the best mean of 3 consecutive solves</li>
                <li>Works for all time-based events (3x3, 4x4, 5x5, etc.)</li>
                <li>Works for FMC (Fewest Moves Challenge) with move counts</li>
                <li>Clickable to show detailed breakdown</li>
                <li>Supports all languages (English, Arabic, Kurdish)</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>How Best mo3 is Calculated</h2>
        <p>Mean of 3 (mo3) is different from Average of 5 (ao5). In mo3, we take the simple average of 3 consecutive times without removing best and worst.</p>
        
        <h3>Example Calculation:</h3>
        <div class="times-list">
            <div class="time-item">12.45</div>
            <div class="time-item">11.23</div>
            <div class="time-item">13.67</div>
            <div class="time-item">10.89</div>
            <div class="time-item">12.34</div>
        </div>
        
        <div class="calculation">
            <strong>Possible mo3s:</strong><br>
            • Times 1-3: (12.45 + 11.23 + 13.67) ÷ 3 = <strong>12.45</strong><br>
            • Times 2-4: (11.23 + 13.67 + 10.89) ÷ 3 = <strong>11.93</strong><br>
            • Times 3-5: (13.67 + 10.89 + 12.34) ÷ 3 = <strong>12.30</strong><br>
            <br>
            <span class="best-mo3">Best mo3: <strong>11.93</strong> (from times 2-4)</span>
        </div>
    </div>

    <div class="test-section">
        <h2>Implementation Details</h2>
        
        <h3>Files Modified:</h3>
        <div class="code-block">
• index.html - Added Best mo3 stat card to overview section
• assets/js/cubing-timer.js - Added calculation logic
• assets/js/lang/en.js - Added "bestMo3" translation
• assets/js/lang/ar.js - Added Arabic translation
• assets/js/lang/ckb.js - Added Kurdish translation
        </div>

        <h3>Key Functions:</h3>
        <div class="code-block">
• updateOverviewStats() - Regular time-based events
• updateFMCOverviewStats() - FMC (Fewest Moves Challenge)
• calculateMean() - Helper function for mo3 calculation
        </div>
    </div>

    <div class="test-section">
        <h2>Testing Instructions</h2>
        <ol>
            <li>Open scTimer in your browser</li>
            <li>Complete at least 3 solves for any event</li>
            <li>Click "More Stats" button</li>
            <li>Look for "Best mo3" in the Overview section</li>
            <li>Click on the Best mo3 card to see detailed breakdown</li>
            <li>Test with different events (3x3, 4x4, FMC, etc.)</li>
        </ol>
        
        <div class="feature-demo">
            <h3>Expected Behavior:</h3>
            <ul>
                <li>Shows "-" if less than 3 solves</li>
                <li>Shows "DNF" if any solve in the best mo3 is DNF</li>
                <li>Shows formatted time for regular events</li>
                <li>Shows move count for FMC events</li>
                <li>Card is clickable when data is available</li>
                <li>Card is not clickable when no data</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>Events That Use mo3</h2>
        <p>The following WCA events use mean of 3 instead of average of 5:</p>
        <div class="times-list">
            <div class="time-item">6x6x6</div>
            <div class="time-item">7x7x7</div>
            <div class="time-item">3x3 BLD</div>
            <div class="time-item">4x4 BLD</div>
            <div class="time-item">5x5 BLD</div>
            <div class="time-item">3x3 MBLD</div>
            <div class="time-item">FMC</div>
        </div>
        <p>For these events, the Best mo3 statistic is particularly relevant as it matches the official WCA format.</p>
    </div>

    <script>
        // Add some interactive elements to demonstrate the feature
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Best mo3 feature test page loaded');
            console.log('Feature successfully implemented in scTimer');
        });
    </script>
</body>
</html>
