// Italian language file
export default {
  dir: "ltr",
  settings: { title: "Impostazioni", save: "Salva", close: "<PERSON>udi", language: "Lingua" },
  info: {
    title: "Informazioni",
    pwaInstall: {
      title: "Installa come App",
      description: "Installa scTimer come Progressive Web App per la migliore esperienza.",
      install: "Installa App",
    },
    shortcuts: { title: "Scorciatoie da Tastiera", timer: "Controlli Timer", spacebar: "Avvia/ferma timer" },
    features: { title: "Caratteristiche Principali", timer: "Timer Professionale", puzzles: "Tutti gli Eventi WCA" },
  },
  timerOptions: {
    title: "Opzioni Timer",
    useInspection: "Usa Ispezione WCA (15s)",
    timerMode: "Modalità Timer:",
    timerModeTimer: "Timer",
  },
  displayOptions: { title: "Opzioni Display", darkMode: "Modalità Scura" },
  app: { title: "scTimer", description: "Un timer per speedcubing con ispezione WCA e statistiche" },
  timer: { ready: "Pronto", running: "In esecuzione", idle: "Inattivo", inspection: "Ispezione" },
  stats: { title: "Statistiche", best: "Migliore", worst: "Peggiore", mean: "Media", solves: "Risoluzioni" },
  statsDetails: { title: "Dettagli Statistiche", overview: "Panoramica", averages: "Medie" },
  solveDetails: { title: "Dettagli Risoluzione", time: "Tempo", date: "Data", scramble: "Scramble" },
  times: { title: "Tempi di Risoluzione", clear: "Cancella Tempi", close: "Chiudi" },
  buttons: { viewTimes: "Vedi Tempi", ok: "OK", cancel: "Annulla" },
  events: {
    333: "3×3×3", 222: "2×2×2", 444: "4×4×4", 555: "5×5×5", 666: "6×6×6", 777: "7×7×7",
    "333bf": "3×3×3 Bendato", "333fm": "3×3×3 Meno Mosse", "333oh": "3×3×3 Una Mano",
    clock: "Clock", minx: "Megaminx", pyram: "Pyraminx", skewb: "Skewb", sq1: "Square-1",
  },
  mbld: { cubeCount: "Cubi", points: "punti", save: "Salva Risultato" },
  modals: { error: "Errore", warning: "Avviso", info: "Informazione", confirm: "Conferma" },
  stackmat: { error: "Errore Stackmat", connected: "Connesso", disconnected: "Disconnesso" },
  sessions: { newSessionTitle: "Nuova Sessione", create: "Crea", save: "Salva" },
  scramble: { loading: "Caricamento scramble..." },
  fmc: { title: "Sfida Meno Mosse", moves: "mosse", submit: "Invia" },
  tutorial: {
    welcomeTitle: "Benvenuto in scTimer!",
    selectLanguage: "Seleziona Lingua:",
    skipTutorial: "Salta Tutorial",
    startTour: "Inizia Tour",
    previous: "Precedente", next: "Successivo", finish: "Fine", close: "Chiudi",
  },
};
