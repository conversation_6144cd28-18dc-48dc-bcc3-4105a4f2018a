<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MBLD Timer Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .timer-display {
            font-size: 48px;
            font-family: monospace;
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: #333;
            color: #0f0;
            border-radius: 4px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .test-btn {
            background: #007bff;
            color: white;
        }
        .test-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>MBLD Timer Storage Fix Test</h1>
    
    <div class="test-section">
        <h2>Problem Description</h2>
        <p>The timer for multi-blind was storing time incorrectly. When the timer was stopped at <strong>37:22.432</strong>, it was being stored as <strong>37.000</strong> milliseconds instead of the correct <strong>2,242,432</strong> milliseconds.</p>
        
        <h3>Root Cause</h3>
        <p>The MBLD manager was trying to parse the timer display text (e.g., "37:22.432") using <code>parseFloat()</code>, which only reads the first number before the colon, resulting in just "37" instead of the full time.</p>
    </div>

    <div class="test-section">
        <h2>Test: Timer Display Parsing (Old Method)</h2>
        <div class="timer-display" id="timer-display">37:22.432</div>
        <button class="test-btn" onclick="testOldMethod()">Test Old Parsing Method</button>
        <div id="old-method-result"></div>
    </div>

    <div class="test-section">
        <h2>Test: Direct Elapsed Time (New Method)</h2>
        <p>Elapsed time: <strong>2,242,432 milliseconds</strong> (37 minutes, 22 seconds, 432 milliseconds)</p>
        <button class="test-btn" onclick="testNewMethod()">Test New Method</button>
        <div id="new-method-result"></div>
    </div>

    <div class="test-section">
        <h2>Verification</h2>
        <button class="test-btn" onclick="runAllTests()">Run All Tests</button>
        <div id="verification-result"></div>
    </div>

    <script>
        function testOldMethod() {
            const timerElement = document.getElementById('timer-display');
            const timeInMs = parseFloat(timerElement.textContent) * 1000;
            
            const resultDiv = document.getElementById('old-method-result');
            resultDiv.innerHTML = `
                <div class="test-result error">
                    <strong>Old Method Result:</strong><br>
                    Timer display: "${timerElement.textContent}"<br>
                    parseFloat() result: ${parseFloat(timerElement.textContent)}<br>
                    Stored time: ${timeInMs} ms<br>
                    <strong>❌ INCORRECT!</strong> Should be 2,242,432 ms
                </div>
            `;
        }

        function testNewMethod() {
            // Simulate the actual elapsed time that would be captured
            const elapsedTime = 2242432; // 37:22.432 in milliseconds
            
            const resultDiv = document.getElementById('new-method-result');
            resultDiv.innerHTML = `
                <div class="test-result success">
                    <strong>New Method Result:</strong><br>
                    Direct elapsed time: ${elapsedTime} ms<br>
                    Formatted: ${formatTime(elapsedTime)}<br>
                    <strong>✅ CORRECT!</strong> Preserves full precision
                </div>
            `;
        }

        function formatTime(timeInMs) {
            const totalSeconds = timeInMs / 1000;
            const minutes = Math.floor(totalSeconds / 60);
            const seconds = Math.floor(totalSeconds % 60);
            const milliseconds = timeInMs % 1000;
            
            return `${minutes}:${seconds.toString().padStart(2, '0')}.${milliseconds.toString().padStart(3, '0')}`;
        }

        function runAllTests() {
            testOldMethod();
            testNewMethod();
            
            const resultDiv = document.getElementById('verification-result');
            resultDiv.innerHTML = `
                <div class="test-result success">
                    <strong>Fix Summary:</strong><br>
                    ✅ Modified showMBLDResultsModal() to accept elapsedTime parameter<br>
                    ✅ Added mbldElapsedTime variable to store the actual time<br>
                    ✅ Updated saveMBLDResult() to use stored time instead of parsing display<br>
                    ✅ Timer now correctly stores 2,242,432 ms instead of 37,000 ms<br>
                    <br>
                    <strong>Files Modified:</strong><br>
                    • assets/js/cubing-timer.js (line 1875)<br>
                    • assets/js/mbld-manager.js (lines 14, 1027, 1083)
                </div>
            `;
        }
    </script>
</body>
</html>
