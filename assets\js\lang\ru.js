// Russian language file
export default {
  dir: "ltr",
  settings: {
    title: "Настройки",
    save: "Сохранить",
    close: "Закрыть",
    language: "Язык",
  },
  info: {
    title: "Информация",
    pwaInstall: {
      title: "Установить как приложение",
      description: "Установите scTimer как прогрессивное веб-приложение для лучшего опыта. Работает офлайн и ощущается как нативное приложение.",
      install: "Установить приложение",
      iosTitle: "Установка iOS/iPad:",
      iosStep1: "1. Нажмите кнопку Поделиться",
      iosStep2: '2. Прокрутите вниз и нажмите "На экран Домой"',
      iosStep3: '3. Нажмите "Добавить" для установки',
      note: "Доступно в Chrome, Safari и других современных браузерах",
    },
    shortcuts: {
      title: "Горячие клавиши",
      timer: "Управление таймером",
      spacebar: "Запуск/остановка таймера",
      escape: "Отменить инспекцию и закрыть модальные окна",
      navigation: "Навигация и действия",
      generate: "Генерировать новую скрамбл",
      list: "Переключить список времён",
      settings: "Открыть настройки",
      edit: "Редактировать текущую скрамбл",
      copy: "Копировать скрамбл в буфер обмена",
      stats: "Открыть подробную статистику",
      display: "Переключить отображение",
      visualization: "Переключить визуализацию головоломки",
      statistics: "Переключить отображение статистики",
      darkMode: "Переключить тёмный режим",
      inspection: "Переключить WCA инспекцию",
      penalties: "Управление штрафами",
      removePenalty: "Убрать штраф с последней сборки",
      addPlus2: "Добавить штраф +2 к последней сборке",
      addDNF: "Добавить штраф DNF к последней сборке",
      session: "Управление сессиями",
      emptySession: "Очистить текущую сессию",
      exportSession: "Экспортировать текущую сессию",
      eventSwitching: "Переключение дисциплин",
      alt2to7: "Переключиться на кубики 2×2×2 до 7×7×7",
      altP: "Переключиться на Пираминкс",
      altM: "Переключиться на Мегаминкс",
      altC: "Переключиться на Клок",
      altS: "Переключиться на Скьюб",
      alt1: "Переключиться на Скваер-1",
      altF: "Переключиться на 3×3×3 Наименьшее количество ходов",
      altO: "Переключиться на 3×3×3 Одной рукой",
      blindfolded: "Дисциплины вслепую",
      altCtrl3: "Переключиться на 3×3×3 Вслепую",
      altCtrl4: "Переключиться на 4×4×4 Вслепую",
      altCtrl5: "Переключиться на 5×5×5 Вслепую",
      altCtrl6: "Переключиться на 3×3×3 Мульти-вслепую",
      sessionMgmt: "Управление сессиями",
      altN: "Создать новую сессию",
    },
    gestures: {
      title: "Мобильные жесты",
      swipeDown: "Свайп вниз",
      swipeDownDesc: "Удалить последнюю сборку",
      swipeUp: "Свайп вверх",
      swipeUpDesc: "Переключить штрафы (нет/+2/DNF)",
      swipeLeft: "Свайп влево",
      swipeLeftDesc: "LTR: Новая скрамбл | RTL: Список времён",
      swipeRight: "Свайп вправо",
      swipeRightDesc: "LTR: Список времён | RTL: Новая скрамбл",
      doubleClick: "Двойной клик",
      doubleClickDesc: "Копировать текущую скрамбл (ПК/Мобильный)",
      longPress: "Долгое нажатие/Клик и удержание",
      longPressDesc: "Редактировать текущую скрамбл (ПК/Мобильный)",
    },
    features: {
      title: "Основные функции",
      timer: "Профессиональный таймер",
      timerDesc: "WCA-совместимый хронометраж с режимом инспекции",
      puzzles: "Все WCA дисциплины",
      puzzlesDesc: "Полная поддержка всех официальных WCA дисциплин",
      statistics: "Продвинутая статистика",
      statisticsDesc: "Подробная аналитика с ao5, ao12, ao100",
      scrambles: "Официальные скрамблы",
      scramblesDesc: "Генерация скрамблов по стандарту WCA с 2D визуализацией",
      multilingual: "Многоязычная поддержка",
      multilingualDesc: "15+ языков с поддержкой RTL",
      sync: "Синхронизация Google Drive",
      syncDesc: "Кроссплатформенная синхронизация с умным слиянием",
    },
    sync: {
      title: "Синхронизация Google Drive",
      description: "Синхронизируйте ваши времена сборки на всех устройствах используя Google Drive. Ваши данные безопасно хранятся в вашем личном аккаунте Google Drive.",
      secure: "Безопасно и приватно",
      automatic: "Автоматическая синхронизация",
      offline: "Поддержка офлайн",
      smartMerge: "Умное слияние",
      note: "Включите синхронизацию Google Drive в настройках, чтобы держать ваши времена синхронизированными на всех устройствах.",
    },
  },
  timerOptions: {
    title: "Настройки таймера",
    warningSounds: "Включить предупреждающие звуки",
    useInspection: "Использовать WCA инспекцию (15с)",
    inspectionSound: "Звук инспекции:",
    inspectionSoundNone: "Нет",
    inspectionSoundVoice: "Голос",
    inspectionSoundBeep: "Сигнал",
    stackmatResetInspection: "Сброс Stackmat запускает инспекцию",
    stackmatResetNote: "Примечание: Работает только когда таймер не на 0.000",
    inputTimer: "Режим ввода таймера (Вводить времена вручную)",
    timerMode: "Режим таймера:",
    timerModeTimer: "Таймер",
    timerModeTyping: "Ввод",
    timerModeStackmat: "Stackmat",
    timerModeBluetooth: "Bluetooth (Скоро)",
    microphoneInput: "Ввод микрофона",
    microphoneAuto: "Автоопределение",
    microphoneNote: "Выберите ваш Y-разветвитель или внешний микрофон",
    decimalPlaces: "Десятичные знаки:",
    decimalPlacesNone: "Нет (12)",
    decimalPlaces1: "1 (12.3)",
    decimalPlaces2: "2 (12.34)",
    decimalPlaces3: "3 (12.345)",
  },
  displayOptions: {
    title: "Настройки отображения",
    showVisualization: "Показать визуализацию головоломки",
    showStats: "Показать статистику",
    showDebug: "Показать отладочную информацию",
    darkMode: "Тёмный режим",
    showFMCKeyboard: "Показать FMC клавиатуру",
    scrambleFontSize: "Размер шрифта скрамбла",
  },
  app: {
    title: "scTimer",
    description: "Таймер для спидкубинга с WCA инспекцией и статистикой",
    enterTime: "Ввести время",
    enterSolveTime: "Ввести время сборки вручную",
    generateScrambles: "Генерировать скрамблы",
    outOf: "Из:",
    numberOfCubes: "Количество кубиков (минимум 2):",
    numberOfCubesSolved: "Количество собранных кубиков:",
  },
  timer: {
    ready: "Готов",
    running: "Работает",
    idle: "Ожидание",
    inspection: "Инспекция",
    holding: "Удержание",
  },
  stats: {
    title: "Статистика",
    best: "Лучшее",
    worst: "Худшее",
    mean: "Среднее",
    avg5: "ao5",
    avg12: "ao12",
    mean3: "mo3",
    bestMo3: "Лучшее mo3",
    avg100: "ao100",
    avg1000: "ao1000",
    solves: "Сборки",
    attempts: "Попытки",
    moreStats: "Больше статистики",
  },
  statsDetails: {
    title: "Детали статистики",
    titleFor: "Детали статистики для",
    overview: "Обзор",
    averages: "Средние",
    records: "Рекорды",
    timeDistribution: "Распределение времён",
    progressChart: "График прогресса",
    sessionAnalysis: "Анализ сессии",
    predictions: "Прогнозы",
    standardDeviation: "Стандартное отклонение",
    bestSingle: "Лучший сингл",
    bestAo5: "Лучший ao5",
    bestAo12: "Лучший ao12",
    bestAo100: "Лучший ao100",
    bestAo1000: "Лучший ao1000",
    totalTime: "Общее время",
    averageTime: "Среднее время",
    solvesPerHour: "Сборок/час",
    consistency: "Стабильность",
    nextAo5: "Следующая цель ao5",
    nextAo12: "Следующая цель ao12",
    improvementRate: "Скорость улучшения",
    targetTime: "Целевое время",
    currentSession: "Текущая сессия",
    allSessions: "Все сессии",
    importTimes: "Импорт времён",
    exportJSON: "Экспорт JSON",
    exportCSV: "Экспорт CSV",
  },
  solveDetails: {
    title: "Детали сборки",
    time: "Время",
    date: "Дата",
    scramble: "Скрамбл",
    editedScramble: "Отредактированная скрамбл",
    copyScramble: "Копировать скрамбл",
    penalty: "Штраф",
    none: "Нет",
    comment: "Комментарий",
    addComment: "Добавить комментарий...",
    save: "Сохранить",
    share: "Поделиться",
    plusTwo: "+2",
    dnf: "DNF",
  },
  gestures: {
    scrambleCopied: "Скрамбл скопирована",
    noSolvesToDelete: "Нет сборок для удаления",
    solveDeleted: "Сборка удалена",
    cannotAddPenaltyMBLD: "Нельзя добавить штраф к MBLD сборке",
    dnfRemoved: "DNF убран",
    dnfAdded: "DNF добавлен",
    plus2Added: "Штраф +2 добавлен",
    penaltyRemoved: "Штраф убран",
    newScrambleGenerated: "Новая скрамбл сгенерирована",
    timesPanelOpened: "Панель времён открыта",
  },
  times: {
    title: "Времена сборок",
    clear: "Очистить времена",
    close: "Закрыть",
    delete: "Удалить время",
    confirmClear: "Вы уверены, что хотите очистить все времена для этой дисциплины?",
    confirmDelete: "Вы уверены, что хотите удалить это время?",
  },
  buttons: {
    viewTimes: "Посмотреть времена",
    ok: "ОК",
    cancel: "Отмена",
  },
  events: {
    333: "3×3×3",
    222: "2×2×2",
    444: "4×4×4",
    555: "5×5×5",
    666: "6×6×6",
    777: "7×7×7",
    "333bf": "3×3×3 Вслепую",
    "333fm": "3×3×3 Наименьшее количество ходов",
    "333oh": "3×3×3 Одной рукой",
    clock: "Клок",
    minx: "Мегаминкс",
    pyram: "Пираминкс",
    skewb: "Скьюб",
    sq1: "Скваер-1",
    "444bf": "4×4×4 Вслепую",
    "555bf": "5×5×5 Вслепую",
    "333mbf": "3×3×3 Мульти-вслепую",
  },
